[gd_scene load_steps=5 format=3 uid="uid://han6s0u2osdx"]

[ext_resource type="Script" uid="uid://cyank1t53s6cd" path="res://ground.gd" id="1_yh8r4"]

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_yh8r4"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_lv0v5"]
size = Vector2(1300, 20)

[sub_resource type="GDScript" id="GDScript_t7tip"]
script/source = "extends CollisionShape2D
"

[node name="Ground" type="StaticBody2D"]
physics_material_override = SubResource("PhysicsMaterial_yh8r4")
script = ExtResource("1_yh8r4")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(575, 680)
shape = SubResource("RectangleShape2D_lv0v5")
script = SubResource("GDScript_t7tip")
