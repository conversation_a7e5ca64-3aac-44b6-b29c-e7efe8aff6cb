[gd_scene load_steps=4 format=3 uid="uid://b27m822s1lt3o"]

[ext_resource type="Script" uid="uid://brypx2ml0bxvc" path="res://scene/player/coin.gd" id="1_vohev"]
[ext_resource type="Texture2D" uid="uid://k12hgah1biqa" path="res://coin.svg" id="2_vohev"]

[sub_resource type="CircleShape2D" id="CircleShape2D_uvcn7"]
radius = 16.124516

[node name="coin" type="Area2D"]
script = ExtResource("1_vohev")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_vohev")

[node name="CollisionShape2D" type="CollisionShape2D" parent="." groups=["coin"]]
position = Vector2(0, -1)
shape = SubResource("CircleShape2D_uvcn7")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
