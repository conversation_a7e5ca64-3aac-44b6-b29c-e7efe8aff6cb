extends Node

func _ready():
	print("=== 碰撞测试开始 ===")
	
	# 测试场景中的节点
	var main_scene = get_tree().current_scene
	print("主场景: ", main_scene.name)
	
	# 查找地面节点
	var ground = main_scene.find_child("Ground")
	if ground:
		print("找到地面节点: ", ground.name, " 类型: ", ground.get_class())
		print("地面所在组: ", ground.get_groups())
	else:
		print("未找到地面节点!")
	
	# 查找硬币节点
	var coins = get_tree().get_nodes_in_group("coin")
	print("硬币数量: ", coins.size())
	
	print("=== 碰撞测试结束 ===")
