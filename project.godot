; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="day01"
run/main_scene="uid://b4lx6t0wk5s4"
config/features=PackedStringArray("4.5", "Mobile")
config/icon="res://icon.svg"

[input]

left={
"deadzone": 0.2,
"events": [Object(InputEventKey,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":4194319,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}
right={
"deadzone": 0.2,
"events": [Object(InputEvent<PERSON><PERSON>,"resource_local_to_scene":false,"resource_name":"","device":0,"window_id":0,"alt_pressed":false,"shift_pressed":false,"ctrl_pressed":false,"meta_pressed":false,"pressed":false,"keycode":0,"physical_keycode":4194321,"key_label":0,"unicode":0,"location":0,"echo":false,"script":null)
]
}
up={
"deadzone": 0.2,
"events": []
}
down={
"deadzone": 0.2,
"events": []
}

[rendering]

renderer/rendering_method="mobile"
