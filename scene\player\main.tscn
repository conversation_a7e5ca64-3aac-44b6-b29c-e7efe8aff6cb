[gd_scene load_steps=6 format=3 uid="uid://b4lx6t0wk5s4"]

[ext_resource type="PackedScene" uid="uid://cr0v4mhvirt5b" path="res://scene/player/player01.tscn" id="1_13mp8"]
[ext_resource type="Script" uid="uid://chev6kklu7cpc" path="res://scene/player/main.gd" id="1_eosry"]
[ext_resource type="PackedScene" uid="uid://b27m822s1lt3o" path="res://scene/player/coin.tscn" id="2_l3vyo"]
[ext_resource type="PackedScene" uid="uid://han6s0u2osdx" path="res://ground.tscn" id="4_d5q2v"]
[ext_resource type="PackedScene" uid="uid://s86aymk80vc0" path="res://left_wall.tscn" id="5_rf72f"]

[node name="Main" type="Node2D" groups=["coin"]]
script = ExtResource("1_eosry")
coin_scene = ExtResource("2_l3vyo")

[node name="Player01" parent="." instance=ExtResource("1_13mp8")]
position = Vector2(528, 577)

[node name="Timer" type="Timer" parent="."]
autostart = true

[node name="Ground" parent="." instance=ExtResource("4_d5q2v")]
position = Vector2(616, 682)

[node name="LeftWall" parent="." instance=ExtResource("5_rf72f")]
position = Vector2(5, 123)

[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
