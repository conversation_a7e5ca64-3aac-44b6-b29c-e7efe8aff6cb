extends Area2D
@export var speed:int=200
func _physics_process(delta: float) -> void:
	position+=Vector2.DOWN*speed*delta


func _on_body_entered(body: Node2D) -> void:
	print("检测到碰撞！物体名称: ", body.name, " 类型: ", body.get_class())
	print("物体所在的组: ", body.get_groups())

	if body.is_in_group("player"):
		print("玩家收集硬币")
		queue_free()
	elif body.is_in_group("ground"):
		print("硬币落地")
		queue_free()
	else:
		print("未知物体碰撞")
