[gd_scene load_steps=4 format=3 uid="uid://cr0v4mhvirt5b"]

[ext_resource type="Script" uid="uid://iqrijpw3bm20" path="res://scipts/player/player_01.gd" id="1_3m0fe"]
[ext_resource type="Texture2D" uid="uid://clb5xs73qyqc7" path="res://jubaopeng.svg" id="2_3m0fe"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_3m0fe"]
size = Vector2(135, 70)

[node name="Player01" type="CharacterBody2D" groups=["player"]]
script = ExtResource("1_3m0fe")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(2, 2)
texture = ExtResource("2_3m0fe")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.5, 1)
shape = SubResource("RectangleShape2D_3m0fe")
